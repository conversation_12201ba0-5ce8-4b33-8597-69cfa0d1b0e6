import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/withAuth';
import { exportUserData } from '@/lib/gdpr-compliance';
import { addServerLog } from '@/lib/logging';

/**
 * GDPR Data Export API
 * 
 * Allows users to export all their personal data in compliance with GDPR
 * Article 20 (Right to data portability)
 */

export const GET = withAuth(async (_request, { user }) => {
  try {
    const userId = user.id;

    await addServerLog(
      'INFO',
      'User data export requested',
      'gdpr-export',
      userId
    );

    const userData = await exportUserData(userId);

    // Set headers for file download
    const headers = new Headers();
    headers.set('Content-Type', 'application/json');
    headers.set('Content-Disposition', `attachment; filename="user-data-export-${userId}-${new Date().toISOString().split('T')[0]}.json"`);
    headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');

    return new NextResponse(JSON.stringify(userData, null, 2), {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('GDPR export API error:', error);
    
    await addServerLog(
      'ERROR',
      `User data export failed: ${error}`,
      'gdpr-export'
    );

    return NextResponse.json({
      error: 'Failed to export user data',
      message: 'An error occurred while preparing your data export. Please try again later.',
    }, { status: 500 });
  }
});

/**
 * POST /api/gdpr/export
 * Request a data export (same as GET but for form submissions)
 */
export const POST = GET;

// Only allow GET and POST methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method Not Allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method Not Allowed' },
    { status: 405 }
  );
}
