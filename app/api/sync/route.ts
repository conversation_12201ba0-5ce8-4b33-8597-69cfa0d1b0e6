import { patientReq } from '@/lib/ccRequest';
import { getDb, dbSchema } from '@/lib/db';
import { withAuth } from '@/lib/auth/withAuth';
import { NextResponse } from 'next/server';
import type { GetCCPatientType } from '@/lib/types/ccTypes';
import type { TNewContact } from '@/lib/db/schema';
import { eq, and, count } from 'drizzle-orm';

/**
 * Generate a past timestamp in ISO format
 * @param minutes - Number of minutes to subtract from current time
 * @returns ISO timestamp string (e.g., "2022-07-12T12:34:56.000Z")
 */
const getPastTimestamp = (minutes: number): string => {
  const now = new Date();
  const pastTime = new Date(now.getTime() - (minutes * 60 * 1000));
  return pastTime.toISOString();
};

/**
 * Get the preferred phone number from patient data
 * Priority: phoneMobile > phonePersonal > phoneBusiness
 */
function getPreferredPhone(patient: GetCCPatientType): string | null {
  return patient.phoneMobile || patient.phonePersonal || patient.phoneBusiness || null;
}

/**
 * Transform CC patient data to contact format
 */
function transformPatientToContact(patient: GetCCPatientType, userId: string): TNewContact {
  return {
    userId,
    firstName: patient.firstName,
    lastName: patient.lastName || null,
    phone: getPreferredPhone(patient),
    email: patient.email || null,
    company: null,
    notes: null,
  };
}

/**
 * Check if a contact already exists for the user
 */
async function contactExists(db: Awaited<ReturnType<typeof getDb>>, userId: string, firstName: string, lastName: string | null, phone: string | null): Promise<boolean> {
  if (!phone) return false; // Skip contacts without phone numbers

  const existing = await db
    .select({ id: dbSchema.contacts.id })
    .from(dbSchema.contacts)
    .where(
      and(
        eq(dbSchema.contacts.userId, userId),
        eq(dbSchema.contacts.firstName, firstName),
        eq(dbSchema.contacts.lastName, lastName || ''),
        eq(dbSchema.contacts.phone, phone)
      )
    )
    .limit(1);

  return existing.length > 0;
}

/**
 * Sync contacts from CC API to database
 * POST /api/sync
 */
export const POST = withAuth(async (request, { user }) => {
  try {
    const body = await request.json() as { useChangedSince?: boolean };
    const useChangedSince = body.useChangedSince === true;

    const db = await getDb();
    let totalFetched = 0;
    let totalAdded = 0;
    let totalSkipped = 0;
    let page = 1;
    const perPage = 50; // Reasonable batch size

    // Prepare sync parameters
    const syncParams: {
      page: number;
      perPage: number;
      active: boolean;
      sort?: string;
      changedSince?: string;
    } = {
      page,
      perPage,
      active: true,
    };

    // Set changedSince if requested (last 60 minutes for incremental sync)
    if (useChangedSince) {
      syncParams.changedSince = getPastTimestamp(60);
    } else {
      syncParams.sort = "-createdAt";
    }

    // Fetch all pages from CC API
    while (true) {
      syncParams.page = page;

      try {
        const patients = await patientReq.all(syncParams);

        // Break if no more patients
        if (!patients || patients.length === 0) {
          break;
        }

        totalFetched += patients.length;

        // Process each patient
        for (const patient of patients) {
          // Skip patients without required data
          if (!patient.firstName) {
            totalSkipped++;
            continue;
          }

          const phone = getPreferredPhone(patient);

          // Skip patients without phone numbers
          if (!phone) {
            totalSkipped++;
            continue;
          }

          // Check if contact already exists
          const exists = await contactExists(db, user.id, patient.firstName, patient.lastName, phone);

          if (exists) {
            totalSkipped++;
            continue;
          }

          // Transform and insert new contact
          const newContact = transformPatientToContact(patient, user.id);

          try {
            await db.insert(dbSchema.contacts).values(newContact);
            totalAdded++;
          } catch (dbError) {
            console.error('Error inserting contact:', dbError);
            totalSkipped++;
          }
        }

        page++;

        // Safety break to prevent infinite loops
        if (page > 100) {
          console.warn('Sync stopped at page 100 to prevent infinite loop');
          break;
        }

      } catch (apiError) {
        console.error(`Error fetching page ${page}:`, apiError);
        break;
      }
    }

    return NextResponse.json({
      success: true,
      message: `Sync completed successfully`,
      results: {
        totalFetched,
        totalAdded,
        totalSkipped,
        pagesProcessed: page - 1,
      },
    });

  } catch (error) {
    console.error('Sync API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Sync failed',
        message: 'An error occurred while syncing contacts. Please try again.',
      },
      { status: 500 }
    );
  }
});

// Keep GET for backward compatibility (returns sync status)
export const GET = withAuth(async (_request, { user }) => {
  try {
    const db = await getDb();

    // Get user's contact count
    const contactCountResult = await db
      .select({ count: count() })
      .from(dbSchema.contacts)
      .where(eq(dbSchema.contacts.userId, user.id));

    return NextResponse.json({
      status: 'ready',
      userContactCount: contactCountResult[0]?.count || 0,
      message: 'Sync endpoint is ready. Use POST to start sync.',
    });
  } catch (error) {
    console.error('Sync status error:', error);
    return NextResponse.json(
      { error: 'Failed to get sync status' },
      { status: 500 }
    );
  }
});