import { patientReq } from '@/lib/ccRequest';

/**
 * Generate a past timestamp in ISO format
 * @param minutes - Number of minutes to subtract from current time
 * @returns ISO timestamp string (e.g., "2022-07-12T12:34:56.000Z")
 */
const getPastTimestamp = (minutes: number): string => {
  const now = new Date();
  const pastTime = new Date(now.getTime() - (minutes * 60 * 1000));
  return pastTime.toISOString();
};

export const GET = async (request: Request) => {
  const { searchParams } = new URL(request.url);
  const params: {
    page?: number;
    perPage?: number;
    active?: boolean;
    sort?: string;
    changedSince?: string;
  } = {
    page: searchParams.get('page') ? parseInt(searchParams.get('page') as string) : 1,
    perPage: searchParams.get('perPage') ? parseInt(searchParams.get('perPage') as string) : 20,
    active: searchParams.get('active') ? searchParams.get('active') === 'true' : true,
  }

  if (params.changedSince === 'recent') {
    params.changedSince = getPastTimestamp(60);
  } else {
    params.sort = "-createdAt"
  }

  const patients = await patientReq.all(params);
  return Response.json(patients);
};