import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { getServerLogs, LogLevel } from "@/lib/logging"
import { Suspense } from "react"
import { LogFilters } from "./log-filters"
import { LogPagination } from "./log-pagination"
import { LogManagement } from "./log-management"

interface LogsPageProps {
  searchParams: Promise<{
    page?: string;
    level?: LogLevel;
    source?: string;
    search?: string;
  }>;
}

function getLevelColor(level: string): "destructive" | "secondary" | "default" | "outline" {
  switch (level) {
    case "ERROR":
      return "destructive"
    case "WARN":
      return "secondary"
    case "INFO":
      return "default"
    case "DEBUG":
      return "outline"
    default:
      return "default"
  }
}

async function LogsContent({ searchParams }: LogsPageProps) {
  const params = await searchParams;
  const page = parseInt(params.page || '1');
  const level = params.level;
  const source = params.source;
  const search = params.search;

  const { logs, total, totalPages } = await getServerLogs(
    {
      level,
      source,
      search,
    },
    {
      page,
      limit: 10, // Reduced for easier testing of pagination
    }
  );

  return (
    <>
      {/* Filters at the top */}
      <div className="space-y-4">
        <LogFilters />

        <Card>
          <CardHeader>
            <CardTitle>Server Log Entries</CardTitle>
            <CardDescription>
              GDPR compliant system and application logs with automatic data retention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[850px]">
              <div className="space-y-3">
                {logs.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No logs found matching your criteria
                  </div>
                ) : (
                  logs.map((log) => (
                    <div key={log.id} className="flex items-start space-x-4 rounded-lg border p-4">
                      <Badge variant={getLevelColor(log.level)}>{log.level}</Badge>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{log.message}</p>
                          <p className="text-xs text-muted-foreground">
                            {log.timestamp.toLocaleString()}
                          </p>
                        </div>
                        <p className="text-xs text-muted-foreground">Source: {log.source}</p>
                        {log.userId && (
                          <p className="text-xs text-muted-foreground">User: {log.userId}</p>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Info and pagination at the bottom */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-sm text-muted-foreground">
            Showing {logs.length} of {total} logs
            {totalPages > 1 && (
              <span className="ml-2">• {totalPages} pages total</span>
            )}
          </div>
          <LogPagination currentPage={page} totalPages={totalPages} />
        </div>
      </div>
    </>
  );
}

export default function ServerLogsPage({ searchParams }: LogsPageProps) {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Server Logs</h1>
        <p className="text-muted-foreground">Monitor system events and application logs</p>
      </div>

      <Suspense fallback={
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Loading logs...</div>
          </CardContent>
        </Card>
      }>
        <LogsContent searchParams={searchParams} />
      </Suspense>

       {/* GDPR Compliance and Log Management */}
      <LogManagement />
    </div>
  );
}
