"use client"

import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { PhonebookKey } from "@/lib/phonebook-keys"

interface DeleteKeyDialogProps {
  keyData: PhonebookKey | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (keyId: string) => Promise<void>
}

export function DeleteKeyDialog({ 
  keyData, 
  open, 
  onOpenChange, 
  onConfirm 
}: DeleteKeyDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const handleConfirm = async () => {
    if (!keyData) return
    
    setIsDeleting(true)
    try {
      await onConfirm(keyData.id)
      onOpenChange(false)
    } catch (error) {
      console.error('Error deleting key:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  const formatKey = (key: string) => {
    if (key.length <= 12) return key
    return `${key.slice(0, 8)}...${key.slice(-4)}`
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete API Key</AlertDialogTitle>
          <AlertDialogDescription>
            <div className="space-y-3">
              <div>
                Are you sure you want to delete this API key? This action cannot be undone.
              </div>
              {keyData && (
                <div className="bg-muted p-3 rounded-md">
                  <div className="text-sm font-medium">Key to delete:</div>
                  <code className="text-sm font-mono">
                    {formatKey(keyData.key)}
                  </code>
                </div>
              )}
              <div className="text-sm text-destructive font-medium">
                Any applications using this key will immediately lose access to your phonebook data.
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete Key"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
