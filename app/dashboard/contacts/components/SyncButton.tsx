"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Plus, Loader2, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";

interface SyncResult {
  success: boolean;
  message: string;
  results?: {
    totalFetched: number;
    totalAdded: number;
    totalSkipped: number;
    pagesProcessed: number;
  };
  error?: string;
}

interface SyncButtonProps {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  showOptions?: boolean;
  className?: string;
}

export function SyncButton({ 
  variant = "default", 
  size = "default", 
  showOptions = false,
  className 
}: SyncButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [useChangedSince, setUseChangedSince] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const handleSync = async () => {
    if (isLoading) return;

    setIsLoading(true);
    
    try {
      const response = await fetch('/api/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          useChangedSince,
        }),
      });

      const result: SyncResult = await response.json();

      if (response.ok && result.success) {
        toast({
          title: "Sync Completed",
          description: result.results 
            ? `Added ${result.results.totalAdded} new contacts, skipped ${result.results.totalSkipped} duplicates from ${result.results.totalFetched} total records.`
            : result.message,
        });
        
        // Refresh the page to show updated contacts
        router.refresh();
      } else {
        throw new Error(result.error || result.message || 'Sync failed');
      }
    } catch (error) {
      console.error('Sync error:', error);
      toast({
        variant: "destructive",
        title: "Sync Failed",
        description: error instanceof Error 
          ? error.message 
          : "An error occurred while syncing contacts. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (showOptions) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            Sync Contacts
          </CardTitle>
          <CardDescription>
            Import contacts from CliniCore to your phonebook
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="incremental-sync"
              checked={useChangedSince}
              onCheckedChange={(checked) => setUseChangedSince(checked === true)}
              disabled={isLoading}
            />
            <label
              htmlFor="incremental-sync"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Incremental sync (only recent changes)
            </label>
          </div>
          <p className="text-xs text-muted-foreground">
            {useChangedSince 
              ? "Only sync contacts that have been modified in the last hour"
              : "Sync all active contacts from CliniCore"
            }
          </p>
          <Button 
            onClick={handleSync} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Start Sync
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Button 
      variant={variant}
      size={size}
      onClick={handleSync} 
      disabled={isLoading}
      className={className}
    >
      {isLoading ? (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Syncing...
        </>
      ) : (
        <>
          <Plus className="w-4 h-4 mr-2" />
          Sync Contact
        </>
      )}
    </Button>
  );
}
