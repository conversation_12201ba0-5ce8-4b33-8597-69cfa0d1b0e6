"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { Plus, Loader2, ChevronDown, Settings } from "lucide-react";
import { useRouter } from "next/navigation";

interface SyncResult {
  success: boolean;
  hasMore?: boolean;
  page?: number;
  results?: {
    totalFetched: number;
    totalAdded: number;
    totalSkipped: number;
  };
  error?: string;
  message?: string;
}

interface SyncButtonProps {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  className?: string;
  onSyncComplete?: () => void;
  showDropdown?: boolean;
}

export function SyncButton({
  variant = "default",
  size = "default",
  className,
  onSyncComplete,
  showDropdown = false
}: SyncButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [useChangedSince, setUseChangedSince] = useState(false);
  const { toast } = useToast();
  const router = useRouter();



  const handleSync = async () => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      let page = 1;
      let totalAdded = 0;
      let totalSkipped = 0;
      let totalFetched = 0;

      // Loop through pages until no more data
      while (true) {
        const response = await fetch('/api/sync', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            useChangedSince,
            page,
          }),
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
          throw new Error(result.error || result.message || 'Sync failed');
        }

        // Accumulate results
        if (result.results) {
          totalAdded += result.results.totalAdded;
          totalSkipped += result.results.totalSkipped;
          totalFetched += result.results.totalFetched;
        }

        // Break if no more data
        if (!result.hasMore) {
          break;
        }

        page++;

        // Safety break to prevent infinite loops
        if (page > 100) {
          console.warn('Sync stopped at page 100 to prevent infinite loop');
          break;
        }
      }

      toast({
        title: "Sync Completed",
        description: `Added ${totalAdded} new contacts, skipped ${totalSkipped} duplicates from ${totalFetched} total records.`,
      });

      // Call the completion callback if provided, otherwise refresh the page
      if (onSyncComplete) {
        onSyncComplete();
      } else {
        router.refresh();
      }
    } catch (error) {
      console.error('Sync error:', error);
      toast({
        variant: "destructive",
        title: "Sync Failed",
        description: error instanceof Error
          ? error.message
          : "An error occurred while syncing contacts. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };



  if (showDropdown) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            disabled={isLoading}
            className={className}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Sync Contact
                <ChevronDown className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuItem
            onClick={() => {
              setUseChangedSince(false);
              handleSync();
            }}
            disabled={isLoading}
          >
            <Plus className="w-4 h-4 mr-2" />
            Full Sync
            <span className="ml-auto text-xs text-muted-foreground">All contacts</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              setUseChangedSince(true);
              handleSync();
            }}
            disabled={isLoading}
          >
            <Settings className="w-4 h-4 mr-2" />
            Incremental Sync
            <span className="ml-auto text-xs text-muted-foreground">Recent only</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <div className="px-2 py-1.5">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="auto-incremental"
                checked={useChangedSince}
                onCheckedChange={(checked) => setUseChangedSince(checked === true)}
                disabled={isLoading}
              />
              <label
                htmlFor="auto-incremental"
                className="text-xs text-muted-foreground cursor-pointer"
              >
                Default to incremental sync
              </label>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleSync}
      disabled={isLoading}
      className={className}
    >
      {isLoading ? (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Syncing...
        </>
      ) : (
        <>
          <Plus className="w-4 h-4 mr-2" />
          Sync Contact
        </>
      )}
    </Button>
  );
}
