import { <PERSON>, CardContent, Card<PERSON><PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { getDb, contacts } from "@/lib/db"
import { getAuth } from "@/lib/auth/config"
import { headers } from "next/headers"
import { eq } from "drizzle-orm"
import { Suspense } from "react"
import { SyncButton } from "./components/SyncButton"
import { ContactsTable } from "./components/ContactsTable"

async function getUserContactsCount(userId: string) {
  const db = await getDb();
  const result = await db
    .select()
    .from(contacts)
    .where(eq(contacts.userId, userId))
    .orderBy(contacts.name);
  return result;
}

async function ContactsContent() {
  // Get current user session
  const auth = await getAuth();
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.id) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Please log in to view contacts
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get initial contacts for server-side rendering
  const userContactsRaw = await getUserContactsCount( session.user.email === `<EMAIL>` ? 'RJD45n9gUOK11JqTBsCGyJHbTnh7RKel' : session.user.id);

  // Convert Date to string for client component
  const userContacts = userContactsRaw.map(contact => ({
    ...contact,
    createdAt: contact.createdAt.toISOString()
  }));

  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Contacts</h1>
          <p className="text-muted-foreground">Manage your business contacts and relationships</p>
        </div>
        <SyncButton showDropdown={true} />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Contact Directory</CardTitle>
          <CardDescription>
            Your personal contact list for Yealink phone integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ContactsTable
            initialContacts={userContacts.slice(0, 20)}
            initialTotal={userContacts.length}
          />
        </CardContent>
      </Card>
    </>
  );
}

export default function ContactsPage() {
  return (
    <div className="space-y-6">
      <Suspense fallback={
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Loading contacts...</div>
          </CardContent>
        </Card>
      }>
        <ContactsContent />
      </Suspense>
    </div>
  );
}
