# Contact Sync Implementation

## Overview

This implementation provides a complete contact synchronization system that integrates the contacts dashboard with the CliniCore (CC) API endpoints. The system allows users to sync patient data from the CC API into their local contact database.

## Features Implemented

### 1. Enhanced Sync API Route (`/app/api/sync/route.ts`)

**Key Features:**
- **Authentication**: Uses `withAuth` middleware to ensure only authenticated users can sync
- **Pagination**: Implements proper pagination to fetch all patients from CC API
- **Data Transformation**: Extracts firstName, lastName, and phone number from CC patient data
- **Duplicate Prevention**: Checks for existing contacts before inserting new ones
- **Incremental Sync**: Supports `changedSince` parameter for syncing only recent changes
- **Error Handling**: Comprehensive error handling for API failures and database operations
- **Progress Tracking**: Returns detailed sync results including counts of fetched, added, and skipped contacts

**Phone Number Priority:**
The system prioritizes phone numbers in this order:
1. `phoneMobile`
2. `phonePersonal` 
3. `phoneBusiness`

**API Endpoints:**
- `POST /api/sync` - Performs the sync operation
- `GET /api/sync` - Returns sync status and user contact count

### 2. Client-Side Sync Component (`/app/dashboard/contacts/components/SyncButton.tsx`)

**Key Features:**
- **Loading States**: Shows loading spinner during sync operations
- **Error Handling**: Displays error messages using toast notifications
- **Success Feedback**: Shows sync results with detailed statistics
- **Incremental Sync Option**: Checkbox to enable incremental syncing
- **Responsive Design**: Works on both desktop and mobile devices
- **Prevents Multiple Syncs**: Disables button during sync operations

**Component Variants:**
- Simple button variant for header and empty state
- Detailed card variant with options for incremental sync

### 3. Updated Contacts Dashboard (`/app/dashboard/contacts/page.tsx`)

**Key Features:**
- **Hybrid Architecture**: Server-side rendering for initial data, client-side for interactions
- **Sync Integration**: Integrated sync buttons throughout the interface
- **Auto-refresh**: Page refreshes after successful sync to show new contacts
- **Sync Options**: Dedicated sync options card with incremental sync checkbox

## Technical Implementation Details

### Data Flow

1. **User Interaction**: User clicks sync button (with optional incremental sync checkbox)
2. **API Request**: Client sends POST request to `/api/sync` with sync options
3. **Authentication**: Server validates user session using `withAuth`
4. **CC API Pagination**: Server fetches all pages from CC API until no more data
5. **Data Processing**: Each patient record is transformed and checked for duplicates
6. **Database Storage**: New contacts are inserted into the database
7. **Response**: Server returns sync results with statistics
8. **UI Update**: Client displays results and refreshes the page

### Error Handling Strategy

**API Level:**
- Proper HTTP status codes (401, 500, etc.)
- Structured error responses with user-friendly messages
- Logging of errors for debugging

**Client Level:**
- Toast notifications for user feedback
- Loading states to prevent multiple operations
- Graceful degradation on network failures

**Database Level:**
- Transaction safety for data integrity
- Duplicate prevention using database queries
- Proper constraint handling

### Security Considerations

- **Authentication Required**: All sync operations require valid user session
- **User Isolation**: Contacts are associated with specific users
- **Input Validation**: Patient data is validated before database insertion
- **Rate Limiting**: Pagination prevents overwhelming the CC API
- **SQL Injection Prevention**: Uses parameterized queries via Drizzle ORM

## Usage Instructions

### For Users

1. **Navigate** to the Contacts dashboard (`/dashboard/contacts`)
2. **Choose Sync Type**:
   - Uncheck "Incremental sync" for full sync (all active contacts)
   - Check "Incremental sync" for recent changes only (last 60 minutes)
3. **Click "Start Sync"** and wait for completion
4. **View Results** in the success notification
5. **See New Contacts** in the updated contact list

### For Developers

**Testing the Sync API:**
```bash
# Test sync status
curl -X GET http://localhost:3000/api/sync \
  -H "Cookie: your-session-cookie"

# Perform full sync
curl -X POST http://localhost:3000/api/sync \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{"useChangedSince": false}'

# Perform incremental sync
curl -X POST http://localhost:3000/api/sync \
  -H "Content-Type: application/json" \
  -H "Cookie: your-session-cookie" \
  -d '{"useChangedSince": true}'
```

## Configuration

### Environment Variables
The sync functionality uses the existing CC API configuration:
- `CC_API_URL` - CliniCore API base URL
- `CC_API_TOKEN` - Authentication token for CC API

### Sync Parameters
- **Page Size**: 50 records per page (configurable in code)
- **Max Pages**: 100 pages maximum (safety limit)
- **Incremental Window**: 60 minutes for `changedSince` parameter

## Future Enhancements

1. **Real-time Sync**: WebSocket-based real-time updates
2. **Selective Sync**: Allow users to choose which fields to sync
3. **Sync Scheduling**: Automated periodic syncing
4. **Conflict Resolution**: Handle conflicts when contacts are modified in both systems
5. **Bulk Operations**: Batch insert/update for better performance
6. **Sync History**: Track sync operations and their results
