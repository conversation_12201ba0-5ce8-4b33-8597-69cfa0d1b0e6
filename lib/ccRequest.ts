import { getConfig } from '@/lib/config';
import type {
  GetCCPatientType
} from '@/lib/types/ccTypes';

// Simple types
type RequestOptions = {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: Record<string, unknown>;
  params?: Record<string, string | number>;
};

/**
 * Simple HTTP client for ClinicalCare API
 */
const ccRequest = async <T = Record<string, unknown>>(
  options: RequestOptions,
): Promise<T> => {
  const { url, method, data, params } = options;
  const config = getConfig();

  // Build URL
  let fullUrl = `${config.CC_API_URL}${url}`;
  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, String(value));
    });
    fullUrl += `?${searchParams.toString()}`;
  }

  // Make request
  const response = await fetch(fullUrl, {
    method,
    headers: {
      Authorization: `Bearer ${config.CC_API_TOKEN}`,
      'Content-Type': 'application/json',
    },
    body: data ? JSON.stringify(data) : undefined,
  });

  if (!response.ok) {
    throw new Error(`CC API Error: ${response.status} ${response.statusText}`);
  }

  return response.json();
};
/**
 * Patient operations
 */
export const patientReq = {
  /**
   * Get all patients with pagination and filtering
   */
  all: async (params: {
    page?: number;
    perPage?: number;
    active?: boolean;
    sort?: string;
    changedSince?: string;
  } = {
    page: 1,
    perPage: 20,
    active: true,
    sort: "-createdAt",
  }): Promise<GetCCPatientType[]> => {
    const requestParams = {
      [`active${params.active}`]: "",
      "page[number]": params.page || 1,
      "page[size]": params.perPage || 20,
      sort: params.sort || "-createdAt",
    };

    if (params.changedSince) {
      requestParams["changedSince"] = params.changedSince;
    }

    const response = await ccRequest<{ patients: GetCCPatientType[] }>({
      url: "/patients",
      method: "GET",
      params: requestParams,
    });
    return response.patients;
  },
};

export default ccRequest;