import { createAuthClient } from "better-auth/react";
import { inferAdditionalFields } from "better-auth/client/plugins";
import { getClientConfig } from "@/lib/config";
import type { getAuth } from "@/lib/auth/config";

const clientConfig = getClientConfig();

export const authClient = createAuthClient({
  baseURL: clientConfig.APP_URL,
  plugins: [
    inferAdditionalFields<Awaited<ReturnType<typeof getAuth>>>(),
  ],
});

export const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
  $Infer,
} = authClient;
