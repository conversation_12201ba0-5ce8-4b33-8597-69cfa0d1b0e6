import { relations } from "drizzle-orm/relations";
import { user, serverLogs, phonebookKeyPasswords, session, account, contacts } from "./schema";

export const serverLogsRelations = relations(serverLogs, ({one}) => ({
	user: one(user, {
		fields: [serverLogs.userId],
		references: [user.id]
	}),
	phonebookKeyPassword: one(phonebookKeyPasswords, {
		fields: [serverLogs.phonebookKeyPasswordsId],
		references: [phonebookKeyPasswords.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	serverLogs: many(serverLogs),
	sessions: many(session),
	accounts: many(account),
	contacts: many(contacts),
	phonebookKeyPasswords: many(phonebookKeyPasswords),
}));

export const phonebookKeyPasswordsRelations = relations(phonebookKeyPasswords, ({one, many}) => ({
	serverLogs: many(serverLogs),
	user: one(user, {
		fields: [phonebookKeyPasswords.userId],
		references: [user.id]
	}),
}));

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const contactsRelations = relations(contacts, ({one}) => ({
	user: one(user, {
		fields: [contacts.userId],
		references: [user.id]
	}),
}));