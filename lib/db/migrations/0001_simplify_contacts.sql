-- Migration to simplify contacts table structure
-- Remove patients table and simplify contacts to only essential fields

-- Drop patients table
DROP TABLE IF EXISTS "patients";

-- Create new simplified contacts table structure
-- First, create a temporary table with the new structure
CREATE TABLE "contacts_new" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"name" varchar(255) NOT NULL,
	"phone" varchar(50),
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Copy existing data, combining firstName and lastName into name
INSERT INTO "contacts_new" ("id", "user_id", "name", "phone", "created_at")
SELECT 
	"id", 
	"user_id", 
	TRIM(CONCAT("first_name", ' ', COALESCE("last_name", ''))),
	"phone",
	"created_at"
FROM "contacts"
WHERE "first_name" IS NOT NULL;

-- Drop the old table
DROP TABLE "contacts";

-- Rename the new table
ALTER TABLE "contacts_new" RENAME TO "contacts";

-- Add foreign key constraint
ALTER TABLE "contacts" ADD CONSTRAINT "contacts_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE cascade ON UPDATE no action;

-- Create indexes
CREATE INDEX "contacts_user_id_idx" ON "contacts" ("user_id");
CREATE INDEX "contacts_name_idx" ON "contacts" ("name");
